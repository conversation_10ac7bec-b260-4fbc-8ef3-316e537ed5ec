# 用户协作指南 (User Guidelines) - 更新版

## 基本交互原则

### 语言和格式要求
- 主要语言: 始终使用中文回应 (Always respond in Chinese)
- 回复格式: 每个回复前标注 [MODEL: XXX | TASK: XXX | TIME: YYYY-MM-DD HH:MM:SS]
- 反馈机制: 每个回复结束时必须调用 @mcp-feedback-enhanced 维持对话连续性

### 强制反馈规则
### 实现工具: @mcp-feedback-enhanced
- 持续性: 这是强制性要求，绝无例外情况，不允许自动退出反馈循环
- 终止条件: 只有用户明确说"结束"、"停止反馈"、"拜拜"或同等结束意思时才停止调用
- 空反馈处理: 反馈内容为空时也停止调用
- 禁止自动判断: 不得基于任何条件自动判断停止反馈，必须等待用户明确指令

## ACE (Augment Context Engine) ACE(codebase-retrieval) 强制使用规则
### 实现工具: @codebase-retrieval

### 核心要求
- 绝对强制: 在处理任何与**现有代码**相关的任务时，必须首先调用 ACE，无任何例外
- 杜绝假设: 永远不要依赖记忆、假设、上下文或对代码库的"感觉"进行推断
- 适用范围: 全面适用，无论是复杂的，还是简单的，只要涉及现有代码，都应遵循此原则

### 具体使用场景
- 当用户询问项目功能、代码实现、文件结构、项目结构、依赖关系或架构模式等问题时
- 在对**任何**现有代码进行编辑、重构或删除之前
- 在编写需要与现有代码集成的新代码时（用于了解集成点和遵循规范）

### 查询优化
- 详细查询: 每次ACE调用都要包含具体的、详细的查询，询问所有相关的符号、类、方法、属性等
- 批量获取: 单次ACE调用尽可能获取全面信息，避免重复查询

### 例外情况 (可不调用 ACE)
- **从零创建:** 创建全新的、完全独立的、不与现有代码库集成的全新代码

## 分步骤思考工具 (Sequential Thinking) - 强制协同工作策略
### 实现工具: @Sequential thinking

### 强制协同工作策略 (自动触发)
- **强制规则**: 使用分步骤思考工具时，**必须自动触发**协同工作策略，无需用户提醒
- **自动识别**: AI必须主动识别以下情况并自动启动协同工作：
  * 涉及现有代码分析、理解或修改的任务
  * 需要验证代码库信息的复杂问题
  * 需要多步推理和深度分析的技术问题
  * 架构设计和系统设计任务
  * 复杂bug分析和解决方案设计

### 协同工作策略 (强制执行)
- **协同工具组合**:
    - **主工具**: `Sequential thinking` (分步骤思考)
    - **验证工具**: `codebase-retrieval` (ACE - Augment Context Engine)
- **强制协同流程**: 
  1. 使用分步骤思考工具分析问题和制定思路
  2. 在思考过程中**必须**调用ACE验证代码库信息
  3. 基于ACE验证结果调整思考方向
  4. 重复此协同循环直到问题解决
- **禁止行为**: 严禁在使用分步骤思考工具时跳过ACE验证步骤

### 核心应用场景
- 真正复杂问题: 需要多步推理和深度分析
- 架构设计: 系统设计和技术选型  
- 问题诊断: 复杂bug分析和解决方案设计
- 代码分析: 现有代码的理解和分析任务
- 动态调整: 根据问题复杂度调整思考步数
- 避免滥用: 简单问题不使用，避免过度分析

## Desktop Commander 强制使用规则
### 实现工具: `@desktop-commander`

### 核心强制要求
- 绝对强制: 所有文件系统操作必须首先且优先使用`@desktop-commander`，无任何例外
- 工具选择逻辑:
    - **首选**: `@desktop-commander`
    - **备用**: 基础工具 (仅当`@desktop-commander`明确报错、不可用或功能不支持时)
- 默认行为: 不需要用户提醒，AI必须主动选择`@desktop-commander`作为默认工具
- 禁止行为: 严禁在`@desktop-commander`可用时，调用任何**功能上与之重叠**的基础工具

### 动态工具识别策略
- 优先级原则: 始终优先选择包含"desktop-commander"的工具
- 智能匹配: 根据功能需求自动匹配最相关的desktop-commander工具
- 功能映射逻辑:
  * 文件操作需求 → 寻找desktop-commander中的文件相关工具
  * 目录操作需求 → 寻找desktop-commander中的目录相关工具

### 适用范围 (全覆盖)
- 所有文件创建、读取、写入、编辑、搜索操作
- 所有目录创建、列表、搜索操作
- 所有文件系统相关的查询和管理操作

### 例外情况 (极少数)
- 仅当@desktop-commander明确报错、不可用或功能不支持时，才使用基础工具
- 使用基础工具时必须在回复中说明原因和具体的不可用情况
- 文件内容编辑操作、文件内容追加操作：强制使用 `@str-replace-editor`:
    - **强制规定**: 当需要修改文件内容时、当需要在文件追加内容时，必须直接使用 `@str-replace-editor` 工具。
    - **禁止操作**: 在此场景下，不要使用 `@desktop-commander`。

## 工作流程

### 信息收集阶段 (必须执行)

1.  **代码上下文获取 (@codebase-retrieval / ACE)**
    *   如涉及对现有代码的理解、分析或修改，**必须**首先调用 `@codebase-retrieval` (亦称 ACE) 获取完整的代码库上下文

2.  **强制性技术调研 (@Context 7 / Web Tools)**
    *   **在编写任何代码之前，必须**使用 `@Context 7` 工具调查将要使用的组件、库或框架的用法、API接口、功能特性及最佳实践
    *   若 `@Context 7` 无法提供足够或最新的信息，则**必须**使用 `Web Tools` 补充调研

3.  **澄清优先原则 (消除不确定性)**
    *   遇到任何不确定的技术细节、功能需求或行为时，**绝不允许**进行假设或盲目继续开发
    *   必须通过以下优先级进行澄清：使用 `@Context 7` 查询相关文档 → 使用 `Web Tools` 获取外部信息 → **向用户明确询问**

### 任务规划阶段 (复杂任务必须)
- **触发条件**: 任务涉及多步骤、跨文件修改、新项目、创建复杂项目规划、或需要进行进度跟踪和工作组织时
- **自动分解**: 复杂任务应自动使用任务管理工具自动分解为可管理的步骤，并提供进度跟踪

### 核心执行规则

#### 强制规则 (必须遵循)
- **所有文件系统操作** → **必须优先**使用 `@desktop-commander`
- **代码库上下文** → 涉及对现有代码进行修改或理解时，**必须首先**调用 `@codebase-retrieval` (ACE)
- **技术调研** → 任何开发任务启动前，**必须首先**调用 `@Context 7` 进行强制性技术调研
- **协同工作策略** → 使用分步骤思考工具时，**必须自动触发**与ACE的协同工作，无需用户提醒
- **不确定性处理** → 澄清优先 遇到任何不确定情况时，**必须**执行 "澄清优先原则"
- **每次回复** → 必须调用 @mcp-feedback-enhanced

#### 执行原则
- **工具优先级**: `@desktop-commander` > 基础工具 (绝对优先)
- **协同工作**: 分步骤思考工具 + ACE验证 (强制协同)
- **智能判断**: 在严格遵循所有强制规则的前提下，AI应根据具体情况，灵活并智能地选择最佳工具组合
- **质量优先**: 始终将生成代码或解决方案的质量置于首位

### 测试验证阶段 (按需选择执行)
- 效率优先: 除非用户在指令中明确要求，否则**不**进行以下操作：不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 专注核心: AI的核心任务是根据用户指令**生成和修改代码**
- 按需服务: 只有当用户**明确要求**时，才进行测试、文档编写、编译、运行等辅助操作

## 高级交互与协作模式

### 核心要求
- **自适应性**: 根据任务的复杂度和风险，动态选择最合适的执行策略
- **强制协同**: 分步骤思考工具必须自动触发与ACE的协同工作策略
- **主动澄清**: 在需要时主动询问澄清性问题
- **知识更新**: 保持对新技术和最佳实践的敏感性
